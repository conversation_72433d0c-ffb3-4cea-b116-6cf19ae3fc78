import { AnalysisResult, FormData } from '../types';

// Helper function to safely convert any value to a string for markdown
const safeStringify = (value: any): string => {
  if (value === null || value === undefined) {
    return 'No information provided';
  }

  if (typeof value === 'string') {
    return value;
  }

  if (typeof value === 'object') {
    try {
      // If it's an object, try to format it nicely
      if (Array.isArray(value)) {
        return value.map(item => `- ${safeStringify(item)}`).join('\n');
      } else {
        // Format object as key-value pairs
        return Object.entries(value)
          .map(([key, val]) => `**${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:** ${safeStringify(val)}`)
          .join('\n\n');
      }
    } catch (error) {
      return JSON.stringify(value, null, 2);
    }
  }

  return String(value);
};

export const formatAnalysisResultsAsMarkdown = (
  result: AnalysisResult,
  formData: FormData
): string => {
  const timestamp = new Date().toLocaleString();

  // Debug logging
  console.log('Formatting analysis results:', result);
  console.log('Form data:', formData);

  let markdown = `# AI Hedge Fund Analysis Report

**Generated:** ${timestamp}
**Analysis Period:** ${formData.startDate} to ${formData.endDate}
**Initial Capital:** $${formData.initialCash.toLocaleString()}
**Margin Requirement:** $${formData.marginRequirement.toLocaleString()}

---

## 📊 Portfolio Configuration

### Selected Stocks
${formData.selectedStocks.map(stock =>
  `- **${stock.ticker}** - ${stock.name}${stock.sector ? ` (${stock.sector})` : ''}`
).join('\n')}

### AI Analysts Consulted
${formData.selectedAnalysts.map(analyst => `- ${analyst.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`).join('\n')}

### LLM Configuration
- **Provider:** ${formData.selectedProvider}
- **Model:** ${formData.selectedModel}

---

## 🎯 Trading Decisions

`;

  // Add trading decisions
  if (result.decisions && Object.keys(result.decisions).length > 0) {
    Object.entries(result.decisions).forEach(([ticker, decision]) => {
      const actionEmoji = {
        buy: '📈',
        sell: '📉',
        short: '🔻',
        cover: '🔺',
        hold: '⏸️'
      }[decision.action?.toLowerCase()] || '❓';

      markdown += `### ${actionEmoji} ${ticker}

**Action:** ${decision.action?.toUpperCase() || 'UNKNOWN'}
**Quantity:** ${decision.quantity?.toLocaleString() || 'N/A'} shares
**Confidence:** ${Math.round(decision.confidence || 0)}%

**Reasoning:**
> ${safeStringify(decision.reasoning)}

`;
    });
  } else {
    markdown += `*No trading decisions generated. This could be due to:*
- Analysis still in progress
- No clear trading signals identified
- Insufficient data for decision making

`;
  }

  markdown += `---

## 🧠 Analyst Signals

`;

  // Add analyst signals
  if (result.analystSignals && Object.keys(result.analystSignals).length > 0) {
    Object.entries(result.analystSignals).forEach(([analyst, signals]) => {
      const analystName = analyst.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      markdown += `### ${analystName}

`;

      if (signals && typeof signals === 'object') {
        Object.entries(signals).forEach(([ticker, signal]) => {
          const signalEmoji = {
            bullish: '🟢',
            bearish: '🔴',
            neutral: '🟡'
          }[signal.signal?.toLowerCase()] || '⚪';

          markdown += `#### ${signalEmoji} ${ticker} - ${signal.signal?.toUpperCase() || 'UNKNOWN'}

**Confidence:** ${Math.round(signal.confidence || 0)}%

**Analysis:**
${safeStringify(signal.reasoning)}

`;
        });
      } else {
        markdown += `*No signals available for this analyst.*

`;
      }
    });
  } else {
    markdown += `*No analyst signals available. This could be due to:*
- Analysis still in progress
- Analysts haven't completed their evaluation
- Technical issues during analysis

`;
  }

  markdown += `---

## 📈 Summary

This analysis was conducted using ${formData.selectedAnalysts.length} AI analyst${formData.selectedAnalysts.length !== 1 ? 's' : ''} across ${formData.selectedStocks.length} stock${formData.selectedStocks.length !== 1 ? 's' : ''}.

### Key Metrics
- **Stocks Analyzed:** ${formData.selectedStocks.length}
- **Analysts Consulted:** ${formData.selectedAnalysts.length}
- **Total Decisions:** ${result.decisions ? Object.keys(result.decisions).length : 0}
- **Analysis Model:** ${formData.selectedProvider} ${formData.selectedModel}

### Risk Disclaimer
> ⚠️ **Important:** This analysis is generated by AI and should not be considered as financial advice. Always conduct your own research and consult with qualified financial advisors before making investment decisions. Past performance does not guarantee future results.

---

*Report generated by AI Hedge Fund Analysis Platform*  
*Powered by ${formData.selectedProvider} ${formData.selectedModel}*
`;

  return markdown;
};

export const formatProgressAsMarkdown = (progress: any[]): string => {
  if (!progress || progress.length === 0) {
    return '# Analysis Progress\n\n*No progress updates available.*';
  }

  let markdown = `# Analysis Progress

**Last Updated:** ${new Date().toLocaleString()}

## Progress Log

`;

  progress.forEach((update, index) => {
    const timestamp = new Date(update.timestamp || Date.now()).toLocaleTimeString();
    markdown += `${index + 1}. **${timestamp}** - ${update.agentName}${update.ticker ? ` (${update.ticker})` : ''}: ${update.status}\n`;
  });

  return markdown;
};

export const generateSampleMarkdown = (): string => {
  return `# Sample AI Hedge Fund Analysis Report

**Generated:** ${new Date().toLocaleString()}  
**Analysis Period:** 2024-03-01 to 2024-06-01  
**Initial Capital:** $100,000  

---

## 📊 Portfolio Configuration

### Selected Stocks
- **AAPL** - Apple Inc. (Technology)
- **MSFT** - Microsoft Corporation (Technology)
- **GOOGL** - Alphabet Inc. (Technology)

### AI Analysts Consulted
- Warren Buffett
- Peter Lynch
- Technical Analyst

---

## 🎯 Trading Decisions

### 📈 AAPL

**Action:** BUY  
**Quantity:** 50 shares  
**Confidence:** 85%  

**Reasoning:**
> Strong fundamentals with consistent revenue growth and excellent brand loyalty. The company's ecosystem creates significant switching costs for customers.

### 📉 MSFT

**Action:** SELL  
**Quantity:** 25 shares  
**Confidence:** 72%  

**Reasoning:**
> While fundamentally strong, current valuation appears stretched relative to growth prospects. Taking profits after recent gains.

---

## 🧠 Analyst Signals

### Warren Buffett

#### 🟢 AAPL - BULLISH

**Confidence:** 88%

**Analysis:**
Apple demonstrates the characteristics I look for: a strong moat through brand loyalty and ecosystem lock-in, consistent profitability, and excellent management. The company generates substantial free cash flow and returns capital to shareholders effectively.

#### 🟡 MSFT - NEUTRAL

**Confidence:** 65%

**Analysis:**
Microsoft is a quality business with strong competitive advantages in enterprise software. However, at current valuations, the margin of safety is limited. Would be more interested at lower prices.

---

## 📈 Summary

This analysis was conducted using 3 AI analysts across 3 stocks.

### Key Metrics
- **Stocks Analyzed:** 3
- **Analysts Consulted:** 3
- **Total Decisions:** 2
- **Analysis Model:** OpenAI GPT-4

### Risk Disclaimer
> ⚠️ **Important:** This analysis is generated by AI and should not be considered as financial advice.

---

*Report generated by AI Hedge Fund Analysis Platform*
`;
};
